import { useState } from 'react'
import {
  Box,
  CircularProgressDelayedCentered,
  ContainerWithTabsForDataGrid,
  isNonEmptyDateRange,
  type DateRange,
} from '@karoo-ui/core'
import { DateTime } from 'luxon'
import { useHistory, useLocation } from 'react-router-dom'
import { match, P } from 'ts-pattern'

import type { CalendarShortcutId } from 'api/types'
import PageHeader from 'src/components/_containers/PageHeader'
import PageWithMainTableContainer from 'src/components/_containers/PageWithMainTable'
import { useDateRangeShortcutItems } from 'src/hooks/useDateRangeShortcutItems'
import { useStateWithEventHandler } from 'src/hooks/useStateWithEventHandler'
import { useValidatedSearchParams } from 'src/hooks/useValidatedSearchParams'
import { ctIntl } from 'src/util-components/ctIntl'
import IntlTypography from 'src/util-components/IntlTypography'
import { downloadHtmlElementByIdAsPdf } from 'src/util-functions/file-utils'

import { useScorecardConfigurationWeightageQuery } from '../Settings/api/queries'
import { usePeriodGroupScoresQuery } from './api/queries'
import GroupSelectionPanel from './components/GroupSelectionPanel'
import { ScoreButtons } from './components/ScoreButtons'
import {
  COMPARISON_GROUP_COLORS,
  driverScoresEventHandler,
  generateComparisonGroupUUID,
} from './event-handler'
import type { DriverScoresEvent, DriverScoresState } from './event-handler/types'
import DriverScoreOverview from './Overview'
import DriverScoreRanking from './Ranking'
import SingleDriverScore from './SingleDriverScore'
import {
  driverScoresSearchParamsSchema,
  driverScoresTableTabs,
  type DriverScoreTableTab,
  type SelectedComparisonGroup,
} from './types'

const makeGridsContainerTabs = (): Array<{
  label: string
  value: DriverScoreTableTab
}> =>
  driverScoresTableTabs.map((tab) => {
    switch (tab) {
      case 'overview': {
        return { label: ctIntl.formatMessage({ id: 'Overview' }), value: 'overview' }
      }
      case 'ranking': {
        return { label: ctIntl.formatMessage({ id: 'Ranking' }), value: 'ranking' }
      }
    }
  })

const validateFilter = (groups: ReadonlyArray<SelectedComparisonGroup>) =>
  groups.length > 0 &&
  groups.every(
    (g) =>
      g.vehicleGroupIds === 'all' ||
      g.vehicleGroupIds.size > 0 ||
      g.driverGroupIds === 'all' ||
      g.driverGroupIds.size > 0,
  )

export default function DriverScores() {
  const search = useLocation().search

  const validatedParams = useValidatedSearchParams(() => driverScoresSearchParamsSchema)

  // If there is no search params, show the overview page
  if (search === '') {
    return <DriverScoresPage tab="overview" />
  }

  if (validatedParams.status === 'invalid') {
    return null
  }

  return match(validatedParams.data)
    .with({ type: 'tab' }, ({ tab }) => <DriverScoresPage tab={tab ?? 'overview'} />)
    .with({ type: 'driver' }, ({ id, start, end }) => (
      <SingleDriverScore
        driverId={id}
        startDate={DateTime.fromJSDate(new Date(start))}
        endDate={DateTime.fromJSDate(new Date(end))}
      />
    ))
    .exhaustive()
}

const DriverScoresPage = ({
  tab: selectedGridContainerTab,
}: {
  tab: DriverScoreTableTab
}) => {
  const shortcuts = useDateRangeShortcutItems({ todayTimeStrategy: 'noIncludeToday' })
  const [dateRangeOrShortcutId, setDateRangeOrShortcutId] = useState<
    DateRange<DateTime> | CalendarShortcutId
  >(shortcuts.last30Days.id)

  const [state, dispatch] = useStateWithEventHandler<
    DriverScoresState,
    DriverScoresEvent
  >(
    (): DriverScoresState => ({
      comparisonGroups: [
        {
          id: generateComparisonGroupUUID(),
          name: `${ctIntl.formatMessage({ id: 'Group' })} 1`,
          driverGroupIds: 'all',
          vehicleGroupIds: new Set(),
          vehicleTypeId: null,
          color: COMPARISON_GROUP_COLORS[0],
        },
      ],
      editingComparisonGroupMeta: null,
      nextComparisonGroupNumber: 2,
    }),
    (args) => driverScoresEventHandler(args),
  )

  const dateRange =
    typeof dateRangeOrShortcutId === 'string' && dateRangeOrShortcutId in shortcuts
      ? shortcuts[dateRangeOrShortcutId as keyof typeof shortcuts].getValue()
      : (dateRangeOrShortcutId as DateRange<DateTime>)

  const handleDateRangeChange = (newValue: DateRange<DateTime>, context: any) => {
    if (
      context.validationError === null ||
      (context.validationError[0] === null && context.validationError[1] === null)
    ) {
      setDateRangeOrShortcutId(newValue)
    }
  }

  return (
    <PageWithMainTableContainer>
      <Box
        sx={{
          display: 'grid',
          gridTemplateRows: 'auto 1fr',
          height: '100vh',
          width: '100%',
          overflow: 'hidden',
          gap: 2,
        }}
      >
        <PageHeader>
          <Box>
            <IntlTypography
              variant="h5"
              msgProps={{ id: 'scoreCards.driverScore.title' }}
            />
            <IntlTypography msgProps={{ id: 'scoreCards.driverScore.subtitle' }} />
          </Box>
          <PageHeader.ButtonsContainer>
            <ScoreButtons
              onExport={() => {
                downloadHtmlElementByIdAsPdf({
                  elementId: `Scorecard-driverScores-${selectedGridContainerTab}-page`,
                  fileName: `Scorecard-driverscores-${selectedGridContainerTab}.pdf`,
                  displaySvg: true,
                })
              }}
            />
          </PageHeader.ButtonsContainer>
        </PageHeader>

        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: '310px 1fr',
            gap: 2,
            overflow: 'hidden',
          }}
        >
          <Box sx={{ height: '100%', overflow: 'hidden' }}>
            <GroupSelectionPanel
              dateRangeOrShortcutId={dateRangeOrShortcutId}
              onDateRangeChange={handleDateRangeChange}
              groupState={state}
              dispatch={dispatch}
            />
          </Box>

          <DriverScoresContent
            tab={selectedGridContainerTab}
            dateRange={dateRange}
            comparisonGroups={state.comparisonGroups}
          />
        </Box>
      </Box>
    </PageWithMainTableContainer>
  )
}

const DriverScoresContent = ({
  tab: selectedGridContainerTab,
  dateRange,
  comparisonGroups,
}: {
  tab: DriverScoreTableTab
  dateRange: DateRange<DateTime>
  comparisonGroups: DriverScoresState['comparisonGroups']
}) => {
  const history = useHistory()
  const location = useLocation()

  const setSelectedGridContainerTab = (tab: DriverScoreTableTab) => {
    const searchParams = new URLSearchParams(location.search)
    searchParams.set('tab', tab)
    searchParams.set('type', 'tab')

    history.replace({
      pathname: location.pathname,
      search: searchParams.toString(),
    })
  }

  return (
    <Box sx={{ height: '100%', overflow: 'hidden' }}>
      <ContainerWithTabsForDataGrid
        sx={{ backgroundColor: 'transparent' }}
        renderTabs={() => (
          <ContainerWithTabsForDataGrid.Tabs
            value={selectedGridContainerTab}
            onChange={(_e, newValue: DriverScoreTableTab) =>
              setSelectedGridContainerTab(newValue)
            }
          >
            {makeGridsContainerTabs().map(({ label, value }) => (
              <ContainerWithTabsForDataGrid.Tab
                data-testid={`DriverScoresMain-GridContainerTab-${value}`}
                key={value}
                label={label}
                value={value}
              />
            ))}
          </ContainerWithTabsForDataGrid.Tabs>
        )}
      >
        <Box
          display="flex"
          flexDirection="column"
          sx={{ overflowY: 'auto', bgcolor: 'transparent', mx: 1.5, height: '100%' }}
        >
          {isNonEmptyDateRange(dateRange) && validateFilter(comparisonGroups) ? (
            <DriverScoresData
              tab={selectedGridContainerTab}
              dateRange={dateRange}
              comparisonGroups={comparisonGroups}
            />
          ) : null}
        </Box>
      </ContainerWithTabsForDataGrid>
    </Box>
  )
}

const DriverScoresData = ({
  tab: selectedGridContainerTab,
  dateRange,
  comparisonGroups,
}: {
  tab: DriverScoreTableTab
  dateRange: [DateTime, DateTime]
  comparisonGroups: DriverScoresState['comparisonGroups']
}) => {
  const configQuery = useScorecardConfigurationWeightageQuery()
  console.log('data comparisonGroups', comparisonGroups)
  const periodGroupScoresQuery = usePeriodGroupScoresQuery({
    start_date: dateRange[0].toJSDate(),
    end_date: dateRange[1].toJSDate(),
    groups: comparisonGroups,
  })

  return match([configQuery, periodGroupScoresQuery])
    .with(
      P.when(([cQ, pQ]) => cQ.status === 'pending' || pQ.status === 'pending'),
      () => <CircularProgressDelayedCentered />,
    )
    .with(
      [{ status: 'success' }, { status: 'success' }],
      ([{ data: configData }, { data: periodGroupScoresData }]) =>
        match(selectedGridContainerTab)
          .with('overview', () => (
            <DriverScoreOverview
              dateRange={dateRange}
              groups={comparisonGroups}
              configData={configData}
              periodGroupScoresData={periodGroupScoresData}
            />
          ))
          .with('ranking', () => (
            <DriverScoreRanking
              dateRange={dateRange}
              configData={configData}
              periodGroupDriversData={periodGroupScoresData.drivers}
            />
          ))
          .exhaustive(),
    )
    .otherwise(() => null)
}
