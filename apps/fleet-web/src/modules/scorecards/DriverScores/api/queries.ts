import { useQuery } from '@tanstack/react-query'
import { match } from 'ts-pattern'

import { apiCallerNoX } from 'api/api-caller'
import { makeQueryErrorHandlerWithToast } from 'api/helpers'
import type { DriverGroupId, DriverId, DriverScoresComparisonGroupId } from 'api/types'
import type { PromiseResolvedType } from 'src/types'
import { minutesToMs } from 'src/util-functions/functional-utils'
import { createQuery } from 'src/util-functions/react-query-utils'

import { weightageApiKeyToFormPathMap } from '../../constants'
import type { DriverScoresState } from '../event-handler/types'
import {
  categoryAICameraSchema,
  categoryAICollisionSchema,
  categoryEfficiencySchema,
  categoryHarshDrivingSchema,
  categorySpeedingSchema,
  periodGroupScoreCategorySchema,
  type EventCategory,
  type FetchDefaultDriverScores,
  type FetchDriverScore,
  type FetchPeriodGroupScores,
  type GroupDriverDatumWithoutId,
  type PeriodScoreWithoutId,
} from './types'

// validate the group score_breakdowns, would not return if any category not matches
export const validatePeriodGroupScoreCategories = (
  categories: FetchPeriodGroupScores.ApiOutput['group_data'][number]['score_breakdowns'],
) => {
  const result = []
  for (const category of categories) {
    const categoryParsedResult = periodGroupScoreCategorySchema.safeParse(category)

    if (categoryParsedResult.success) {
      result.push(categoryParsedResult.data)
    } else {
      match(category)
        .with({ type: 'speeding' }, (cate) => {
          const speedingParsedResult = categorySpeedingSchema.safeParse(cate)
          if (speedingParsedResult.success) {
            result.push(speedingParsedResult.data)
          } else {
            console.error('Invalid speeding category', speedingParsedResult.error)
          }
        })
        .with({ type: 'harsh_driving' }, (cate) => {
          const harshDrivingParsedResult = categoryHarshDrivingSchema.safeParse(cate)
          if (harshDrivingParsedResult.success) {
            result.push(harshDrivingParsedResult.data)
          } else {
            console.error(
              'Invalid harsh driving category',
              harshDrivingParsedResult.error,
            )
          }
        })
        .with({ type: 'ai_camera_events' }, (cate) => {
          const aiCameraParsedResult = categoryAICameraSchema.safeParse(cate)

          if (aiCameraParsedResult.success) {
            result.push(aiCameraParsedResult.data)
          } else {
            console.error('Invalid ai camera category', aiCameraParsedResult.error)
          }
        })
        .with({ type: 'ai_collision_events' }, (cate) => {
          const parsedResult = categoryAICollisionSchema.safeParse(cate)
          if (parsedResult.success) {
            result.push(parsedResult.data)
          } else {
            console.error('Invalid ai collision warning category', parsedResult.error)
          }
        })
        .with({ type: 'efficiency_events' }, (cate) => {
          const efficiencyParsedResult = categoryEfficiencySchema.safeParse(cate)
          if (efficiencyParsedResult.success) {
            result.push(efficiencyParsedResult.data)
          } else {
            console.error('Invalid efficiency category', efficiencyParsedResult.error)
          }
        })
        .otherwise(() => null)
    }
  }

  return result
}

export const parsePeriodGroupOrDriverScore = (
  driverOrGroup: GroupDriverDatumWithoutId,
) => {
  const categories = []

  const validResult = validatePeriodGroupScoreCategories(driverOrGroup.score_breakdowns)

  for (const category of validResult) {
    categories.push(
      match(category)
        .with({ type: 'speeding' }, (cate) => ({
          type: cate.type as EventCategory,
          rateType: 'timeBasedRate' as const, // NOTE: speeding would be time based rate
          scoreImpact: cate.total_demerits,
          events: cate.score_breakdown.map((event) => ({
            eventName:
              weightageApiKeyToFormPathMap.find(
                (item) => item.apiKey.toUpperCase() === event.type,
              )?.label ?? event.type,
            eventType: event.type,
            rateType: 'timeBasedRate' as const, // NOTE: speeding would be time based rate
            category: cate.type as EventCategory,
            timeInSecond: event.total_duration,
            prevTimeInSecond: event.previous_total_duration,
            percentage: event.percentage,
            prevPercentage: event.previous_percentage,
            scoreImpact: event.total_demerits,
          })),
          timeInSecond: cate.total_duration,
          prevTimeInSecond: cate.previous_total_duration,
          percentage: cate.percentage,
          prevPercentage: cate.previous_percentage,
        }))
        .otherwise((cate) => ({
          type: cate.type as EventCategory,
          rateType: 'frequencyBasedRate' as const,
          scoreImpact: cate.total_demerits,
          events: cate.score_breakdown.map((event) => ({
            eventName:
              weightageApiKeyToFormPathMap.find(
                (item) => item.apiKey.toUpperCase() === event.type,
              )?.label ?? event.type,
            eventType: event.type,
            rateType: 'frequencyBasedRate' as const,
            category: cate.type as EventCategory,
            eventCount: event.total_events,
            prevEventCount: event.previous_total_events,
            scoreImpact: event.total_demerits,
          })),
          eventCount: cate.total_events,
          prevEventCount: cate.previous_total_events,
        })),
    )
  }

  return {
    categories,
    timeInSecond: driverOrGroup.total_duration ?? 0,
    prevTimeInSecond: driverOrGroup.previous_total_duration ?? 0,
    distanceInKm: Math.round((driverOrGroup.total_distance ?? 0) / 1000),
    prevDistanceInKm: Math.round((driverOrGroup.previous_total_distance ?? 0) / 1000),
  }
}

export const parsePeriodScore = (group: PeriodScoreWithoutId) => ({
  distanceInKm: Math.ceil(group.total_distance / 1000),
  timeInSecond: group.total_duration,
  scores: group.scores,
  avgScore: group.avg_score,
  prevAvgScore: group.previous_avg_score,
})

type FetchPeriodGroupScoresParams = Pick<
  FetchPeriodGroupScores.ApiInput,
  'start_date' | 'end_date'
> & {
  groups: DriverScoresState['comparisonGroups']
}

export const parsePeriodGroupScoresResult = ({
  params,
  response,
}: {
  params: FetchPeriodGroupScoresParams
  response: FetchPeriodGroupScores.ApiOutput
}) => {
  const drivers: Array<{
    id: DriverId
    groupIds: Array<DriverGroupId>
    groupNames: Array<string>
    score: number | null
    prevScore: number | null
    distanceInKm: number
    globalRanking: number | null
  }> = []

  for (const group of response.group_data) {
    for (const driver of group.drivers) {
      const existingDriverIdx = drivers.findIndex(
        (d) => d.id === driver.client_driver_id,
      )
      const groupName =
        params.groups.find((g) => g.id === group.frontend_group_id)?.name ?? ''
      if (existingDriverIdx !== -1) {
        drivers[existingDriverIdx].groupIds.push(
          group.frontend_group_id as DriverGroupId,
        )
        drivers[existingDriverIdx].groupNames.push(groupName)
      } else {
        drivers.push({
          id: driver.client_driver_id as DriverId,
          groupIds: [group.frontend_group_id as DriverGroupId],
          groupNames: [groupName],
          score: driver.avg_score,
          prevScore: driver.previous_avg_score,
          distanceInKm: Math.round((driver.total_distance ?? 0) / 1000),
          globalRanking: driver.position_rank,
        })
      }
    }
  }

  return {
    periodScores: response.period_scores.map((group) => ({
      id: group.frontend_group_id as DriverScoresComparisonGroupId,
      ...parsePeriodScore(group),
    })),
    riskEvents: response.group_data.map((group) => {
      const parsedGroup = parsePeriodGroupOrDriverScore(group)

      return {
        id: group.frontend_group_id as DriverScoresComparisonGroupId,
        ...parsedGroup,
      }
    }),
    driversPerformances: response.group_data.map((group) => ({
      id: group.frontend_group_id as DriverScoresComparisonGroupId,
      drivers: group.drivers.map((driver) => {
        const scoreChanged =
          driver.avg_score !== null && driver.previous_avg_score !== null
            ? driver.avg_score - driver.previous_avg_score
            : null
        return {
          id: driver.client_driver_id as DriverId,
          score: driver.avg_score,
          scoreChanged, // score changed can be null
        }
      }),
    })),
    drivers,
    safetyScore: response.safety_score_target.enabled
      ? response.safety_score_target.value
      : null,
  }
}

async function fetchPeriodGroupScores(params: FetchPeriodGroupScoresParams) {
  console.log('fetchPeriodGroupScores', params)
  const response = await apiCallerNoX<FetchPeriodGroupScores.ApiOutput>(
    'ct_fleet_scorecard_get_groups_score',
    {
      ...params,
      groups: params.groups.map((group) => ({
        id: group.id,
        vehicle_type_id: group.vehicleTypeId,
        driver_group_ids:
          group.driverGroupIds === 'all' ? 'all' : Array.from(group.driverGroupIds),
        vehicle_group_ids:
          group.vehicleGroupIds === 'all' ? 'all' : Array.from(group.vehicleGroupIds),
      })),
    },
  )

  return parsePeriodGroupScoresResult({ params, response })
}

function fetchPeriodGroupScoresQuery(params: FetchPeriodGroupScoresParams) {
  return createQuery({
    queryKey: ['scorecards/fetchGroupPeriodScores', params] as const,
    queryFn: () => fetchPeriodGroupScores(params),
    ...makeQueryErrorHandlerWithToast(),
  })
}

export function usePeriodGroupScoresQuery(params: FetchPeriodGroupScoresParams) {
  return useQuery(fetchPeriodGroupScoresQuery(params))
}

export type FetchPeriodGroupScoresData = PromiseResolvedType<
  typeof fetchPeriodGroupScores
>

/**
 * Fetches default period scores for all drivers
 * @returns Record<client_driver_id, score> - A mapping of driver IDs to their scores
 */
async function fetchDefaultDriverScores() {
  const result = await apiCallerNoX<FetchDefaultDriverScores.ApiOutput>(
    'ct_fleet_scorecard_default_period_scores',
    {},
  )

  return Object.entries(result)
    .filter(([_, score]) => score !== null)
    .map(([driverId, score]) => ({
      id: driverId as DriverId,
      score: score as number,
    }))
    .reduce(
      (acc, curr) => {
        acc[curr.id] = curr.score
        return acc
      },
      {} as Record<DriverId, number>,
    )
}

function fetchDefaultDriverScoresQuery() {
  return createQuery({
    queryKey: ['scorecards/fetchDefaultDriverScores'] as const,
    queryFn: () => fetchDefaultDriverScores(),
    staleTime: minutesToMs(10),
    ...makeQueryErrorHandlerWithToast(),
  })
}

export function useDefaultDriverScoresQuery(options?: { enabled?: boolean }) {
  return useQuery({
    ...fetchDefaultDriverScoresQuery(),
    enabled: options?.enabled,
  })
}

export type FetchDefaultDriverScoresData = PromiseResolvedType<
  typeof fetchDefaultDriverScores
>

export const parseDriverScoreResult = ({
  response,
}: {
  response: FetchDriverScore.ApiOutput
}) => ({
  periodScores: response.period_scores.map((group) => ({
    id: group.client_driver_id as DriverId,
    ...parsePeriodScore(group),
  })),
  drivers: response.ranking.map((driver) => {
    const parsedDriver = parsePeriodGroupOrDriverScore(driver)

    return {
      id: driver.client_driver_id as DriverId,
      ...parsedDriver,
    }
  }),
  safetyScore: response.safety_score_target.enabled
    ? response.safety_score_target.value
    : null,
})

async function fetchDriverScore(params: FetchDriverScore.ApiFnInput) {
  const apiInput = {
    client_driver_id: params.driverId,
    start_date: params.startDate,
    end_date: params.endDate,
    vehicle_type_id: null, // NOTE: preserve these two fields
    vehicle_group_ids: 'all',
  }
  const rawResult = await apiCallerNoX<FetchDriverScore.ApiOutput>(
    'ct_fleet_scorecard_get_drivers_score',
    apiInput,
  )

  return parseDriverScoreResult({ response: rawResult })
}

function fetchDriverScoreQuery(params: FetchDriverScore.ApiFnInput) {
  return createQuery({
    queryKey: ['scorecards/fetchDriverScore', params] as const,
    queryFn: () => fetchDriverScore(params),
    ...makeQueryErrorHandlerWithToast(),
  })
}

export function useFetchDriverScoreQuery(params: FetchDriverScore.ApiFnInput) {
  return useQuery(fetchDriverScoreQuery(params))
}

export type FetchDriverScoreData = PromiseResolvedType<typeof fetchDriverScore>
